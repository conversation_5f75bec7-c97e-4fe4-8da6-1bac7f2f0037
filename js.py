import os
import re
import base64
import jsbeautifier

def beautify_js(js_code):
    """Format JavaScript code for better readability."""
    return jsbeautifier.beautify(js_code)

def replace_obfuscated_variables(js_code):
    """Replace obfuscated variable names with readable ones."""
    js_code = re.sub(r"_0x[0-9a-fA-F]+", "var_deobf", js_code)  # Replace with generic readable names
    return js_code

def decode_base64_strings(js_code):
    """Find and decode Base64 encoded strings in JavaScript."""
    base64_strings = re.findall(r'btoa\(["\'](.*?)["\']\)', js_code)
    for encoded_str in base64_strings:
        try:
            decoded_str = base64.b64decode(encoded_str).decode('utf-8')
            js_code = js_code.replace(f'btoa("{encoded_str}")', f'"{decoded_str}"')
        except Exception:
            pass
    return js_code

def decode_hex_strings(js_code):
    """Find and decode hexadecimal strings in JavaScript."""
    hex_strings = re.findall(r'\\x[0-9a-fA-F]{2}', js_code)
    for hex_str in hex_strings:
        try:
            decoded_char = bytes.fromhex(hex_str.replace('\\x', '')).decode('utf-8')
            js_code = js_code.replace(hex_str, decoded_char)
        except Exception:
            pass
    return js_code

def deobfuscate_js(js_code):
    """Main function to deobfuscate JavaScript."""
    js_code = beautify_js(js_code)
    js_code = replace_obfuscated_variables(js_code)
    js_code = decode_base64_strings(js_code)
    js_code = decode_hex_strings(js_code)
    return js_code

def process_folder(folder_path):
    """Find and deobfuscate all JavaScript files in a folder."""
    for file_name in os.listdir(folder_path):
        if file_name.endswith(".js"):
            file_path = os.path.join(folder_path, file_name)
            with open(file_path, "r", encoding="utf-8") as f:
                js_code = f.read()
            
            deobfuscated_js = deobfuscate_js(js_code)
            
            output_file = os.path.join(folder_path, file_name.replace(".js", "_deobfuscated.js"))
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(deobfuscated_js)
            
            print(f"Deobfuscated: {output_file}")

# Example usage:
if __name__ == "__main__":
    folder_path = input("Enter the folder path containing JavaScript files: ")
    process_folder(folder_path)
    print("Deobfuscation completed for all JS files.")
