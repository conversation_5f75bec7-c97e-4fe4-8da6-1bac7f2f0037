/* Partagily Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

.container {
    background: white;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.logo-text {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10B981;
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background: #EF4444;
}

.status-dot.checking {
    background: #F59E0B;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Authentication Section */
.auth-section {
    padding: 30px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 25px;
    color: #1F2937;
    font-size: 18px;
    font-weight: 600;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.input-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 100%;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #F3F4F6;
    color: #374151;
    border: 1px solid #D1D5DB;
}

.btn-secondary:hover {
    background: #E5E7EB;
}

.btn-small {
    padding: 8px 12px;
    font-size: 12px;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-links {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.auth-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Dashboard Section */
.dashboard-section {
    padding: 20px;
    flex: 1;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: #F9FAFB;
    border-radius: 12px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.user-details h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
}

.user-details p {
    font-size: 13px;
    color: #6B7280;
    margin-bottom: 6px;
}

.subscription-badge {
    background: #10B981;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #6B7280;
    font-weight: 500;
}

.tools-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1F2937;
}

.tools-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 20px;
}

.tool-card {
    background: white;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.tool-card:hover {
    border-color: #667eea;
    transform: translateY(-1px);
}

.tool-card.active {
    border-color: #10B981;
    background: #F0FDF4;
}

.tool-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
    border-radius: 6px;
    overflow: hidden;
}

.tool-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tool-info h4 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 2px;
    color: #1F2937;
}

.tool-info p {
    font-size: 11px;
    color: #6B7280;
}

.current-tool {
    margin-bottom: 20px;
}

.current-tool h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1F2937;
}

.current-tool .tool-card {
    flex-direction: row;
    text-align: left;
    gap: 12px;
}

.current-tool .tool-info {
    flex: 1;
}

.settings-section {
    display: flex;
    gap: 8px;
    margin-top: 20px;
}

.settings-section .btn {
    flex: 1;
}

/* Messages */
.error-message,
.success-message {
    padding: 12px 16px;
    margin: 16px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.error-message {
    background: #FEF2F2;
    color: #DC2626;
    border: 1px solid #FECACA;
}

.success-message {
    background: #F0FDF4;
    color: #059669;
    border: 1px solid #BBF7D0;
}

/* Footer */
.footer {
    padding: 16px 20px;
    border-top: 1px solid #E5E7EB;
    background: #F9FAFB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 16px;
}

.footer-links a {
    color: #6B7280;
    text-decoration: none;
    font-size: 12px;
}

.footer-links a:hover {
    color: #374151;
}

.footer-version {
    font-size: 12px;
    color: #9CA3AF;
}
