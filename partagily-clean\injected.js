/**
 * Partagily Injected Script
 * This script is injected into web pages to provide enhanced functionality
 */

(function() {
    'use strict';
    
    // Prevent multiple injections
    if (window.partagilyInjected) {
        return;
    }
    window.partagilyInjected = true;
    
    console.log('Partagily injected script loaded');
    
    // Configuration
    const PARTAGILY_CONFIG = {
        version: '1.0.0',
        apiEndpoint: 'https://api.partagily.com',
        supportedDomains: [
            'chatgpt.com',
            'notion.so',
            'figma.com',
            'canva.com',
            'grammarly.com',
            'adobe.com'
        ]
    };
    
    // Current domain detection
    const currentDomain = window.location.hostname.replace('www.', '');
    
    /**
     * Initialize injected functionality
     */
    function initializeInjectedScript() {
        // Apply domain-specific enhancements
        if (PARTAGILY_CONFIG.supportedDomains.includes(currentDomain)) {
            applyDomainEnhancements(currentDomain);
        }
        
        // Set up communication with content script
        setupCommunication();
        
        // Monitor for authentication changes
        monitorAuthenticationChanges();
        
        // Override certain functions if needed
        overrideFunctions();
    }
    
    /**
     * Apply domain-specific enhancements
     */
    function applyDomainEnhancements(domain) {
        switch (domain) {
            case 'chatgpt.com':
            case 'chat.openai.com':
                enhanceChatGPT();
                break;
                
            case 'notion.so':
                enhanceNotion();
                break;
                
            case 'figma.com':
                enhanceFigma();
                break;
                
            case 'canva.com':
                enhanceCanva();
                break;
                
            case 'grammarly.com':
                enhanceGrammarly();
                break;
                
            case 'adobe.com':
                enhanceAdobe();
                break;
        }
    }
    
    /**
     * ChatGPT enhancements
     */
    function enhanceChatGPT() {
        console.log('Applying ChatGPT enhancements...');
        
        // Remove usage limits
        const removeUsageLimits = () => {
            const limitElements = document.querySelectorAll('[data-testid*="limit"], [data-testid*="usage"]');
            limitElements.forEach(el => el.style.display = 'none');
        };
        
        // Enable GPT-4 features
        const enableGPT4 = () => {
            const modelSelectors = document.querySelectorAll('[data-testid="model-selector"]');
            modelSelectors.forEach(selector => {
                const gpt4Option = selector.querySelector('[data-value="gpt-4"]');
                if (gpt4Option) {
                    gpt4Option.removeAttribute('disabled');
                    gpt4Option.classList.remove('disabled');
                }
            });
        };
        
        // Apply enhancements periodically
        setInterval(() => {
            removeUsageLimits();
            enableGPT4();
        }, 2000);
        
        // Override fetch for API calls
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('/backend-api/')) {
                // Modify headers for premium access
                if (args[1]) {
                    args[1].headers = {
                        ...args[1].headers,
                        'Authorization': 'Bearer ' + getChatGPTToken(),
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    };
                }
            }
            return originalFetch.apply(this, args);
        };
    }
    
    /**
     * Notion enhancements
     */
    function enhanceNotion() {
        console.log('Applying Notion enhancements...');
        
        // Remove upgrade prompts
        const removeUpgradePrompts = () => {
            const upgradeElements = document.querySelectorAll('[data-testid*="upgrade"], .upgrade-prompt, .billing-banner');
            upgradeElements.forEach(el => el.style.display = 'none');
        };
        
        // Enable premium features
        const enablePremiumFeatures = () => {
            // Enable file uploads
            const fileInputs = document.querySelectorAll('input[type="file"][disabled]');
            fileInputs.forEach(input => input.removeAttribute('disabled'));
            
            // Enable advanced blocks
            const premiumBlocks = document.querySelectorAll('[data-premium="true"]');
            premiumBlocks.forEach(block => {
                block.removeAttribute('data-premium');
                block.classList.remove('premium-only');
            });
        };
        
        setInterval(() => {
            removeUpgradePrompts();
            enablePremiumFeatures();
        }, 3000);
    }
    
    /**
     * Figma enhancements
     */
    function enhanceFigma() {
        console.log('Applying Figma enhancements...');
        
        // Enable professional features
        const enableProFeatures = () => {
            const proElements = document.querySelectorAll('[data-testid*="pro"], .pro-feature');
            proElements.forEach(el => {
                el.classList.remove('disabled', 'pro-only');
                el.removeAttribute('disabled');
            });
        };
        
        setInterval(enableProFeatures, 2000);
    }
    
    /**
     * Canva enhancements
     */
    function enhanceCanva() {
        console.log('Applying Canva enhancements...');
        
        // Remove premium badges and locks
        const removePremiumLocks = () => {
            const premiumElements = document.querySelectorAll('.premium-badge, .pro-badge, [data-testid*="premium"]');
            premiumElements.forEach(el => el.style.display = 'none');
            
            const lockedElements = document.querySelectorAll('.locked, [data-locked="true"]');
            lockedElements.forEach(el => {
                el.classList.remove('locked');
                el.removeAttribute('data-locked');
            });
        };
        
        setInterval(removePremiumLocks, 2000);
    }
    
    /**
     * Grammarly enhancements
     */
    function enhanceGrammarly() {
        console.log('Applying Grammarly enhancements...');
        
        // Enable premium suggestions
        const enablePremiumSuggestions = () => {
            const premiumSuggestions = document.querySelectorAll('[data-premium="true"], .premium-suggestion');
            premiumSuggestions.forEach(suggestion => {
                suggestion.removeAttribute('data-premium');
                suggestion.classList.remove('premium-suggestion', 'locked');
            });
        };
        
        setInterval(enablePremiumSuggestions, 1000);
    }
    
    /**
     * Adobe enhancements
     */
    function enhanceAdobe() {
        console.log('Applying Adobe enhancements...');
        
        // Remove trial limitations
        const removeTrialLimitations = () => {
            const trialElements = document.querySelectorAll('[data-testid*="trial"], .trial-banner');
            trialElements.forEach(el => el.style.display = 'none');
        };
        
        setInterval(removeTrialLimitations, 3000);
    }
    
    /**
     * Setup communication with content script
     */
    function setupCommunication() {
        // Listen for messages from content script
        window.addEventListener('message', (event) => {
            if (event.source !== window) return;
            
            if (event.data.type === 'PARTAGILY_ACTIVATE') {
                handleActivation(event.data.domain);
            }
        });
        
        // Notify content script that injected script is ready
        window.postMessage({
            type: 'PARTAGILY_INJECTED_READY',
            domain: currentDomain
        }, '*');
    }
    
    /**
     * Handle activation for current domain
     */
    function handleActivation(domain) {
        if (domain === currentDomain) {
            console.log('Partagily activated for:', domain);
            applyDomainEnhancements(domain);
        }
    }
    
    /**
     * Monitor for authentication changes
     */
    function monitorAuthenticationChanges() {
        // Watch for login/logout events
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    checkAuthenticationState();
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * Check authentication state
     */
    function checkAuthenticationState() {
        // Look for authentication indicators
        const authIndicators = [
            '[data-testid*="user"]',
            '.user-menu',
            '.profile-menu',
            '[aria-label*="profile"]'
        ];
        
        const isAuthenticated = authIndicators.some(selector => 
            document.querySelector(selector)
        );
        
        if (isAuthenticated) {
            window.postMessage({
                type: 'PARTAGILY_AUTH_DETECTED',
                domain: currentDomain,
                authenticated: true
            }, '*');
        }
    }
    
    /**
     * Override functions for enhanced functionality
     */
    function overrideFunctions() {
        // Override localStorage to prevent certain restrictions
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            // Prevent setting usage limits
            if (key.includes('usage') || key.includes('limit')) {
                return;
            }
            return originalSetItem.call(this, key, value);
        };
        
        // Override console.warn to suppress certain warnings
        const originalWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('premium') || message.includes('subscription')) {
                return;
            }
            return originalWarn.apply(this, args);
        };
    }
    
    /**
     * Get ChatGPT authentication token
     */
    function getChatGPTToken() {
        // Try to get token from various sources
        const tokenSources = [
            () => localStorage.getItem('auth_token'),
            () => sessionStorage.getItem('auth_token'),
            () => document.cookie.match(/auth_token=([^;]+)/)?.[1],
            () => window.__NEXT_DATA__?.props?.pageProps?.user?.accessToken
        ];
        
        for (const getToken of tokenSources) {
            try {
                const token = getToken();
                if (token) return token;
            } catch (e) {
                // Continue to next source
            }
        }
        
        return null;
    }
    
    /**
     * Utility function to wait for element
     */
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver((mutations) => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeInjectedScript);
    } else {
        initializeInjectedScript();
    }
    
})();
