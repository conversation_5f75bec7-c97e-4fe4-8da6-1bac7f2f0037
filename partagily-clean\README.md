# Partagily Chrome Extension

A Chrome extension that provides shared access to premium tools and services through subscription-based authentication and cookie management.

## 🚀 Features

- **Premium Tool Access**: Access 60+ premium tools including ChatGPT Plus, Notion Pro, Figma Professional, Canva Pro, and more
- **Cost Effective**: Shared subscription model reduces individual costs significantly
- **Seamless Integration**: One-click activation for supported tools
- **Secure Authentication**: Encrypted cookie management and secure token handling
- **User-Friendly Interface**: Clean, modern popup interface with real-time status
- **Cross-Domain Support**: Works across multiple premium service domains

## 🛠️ Supported Tools

- **ChatGPT Plus** - AI conversation with GPT-4 access
- **Notion Pro** - Advanced workspace features
- **Figma Professional** - Design collaboration tools
- **Canva Pro** - Premium design templates and features
- **Grammarly Premium** - Advanced writing assistance
- **Adobe Creative Cloud** - Professional creative tools

## 📦 Installation

### Development Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension will appear in your Chrome toolbar

### Production Installation

1. Download from Chrome Web Store (when published)
2. Click "Add to Chrome"
3. Follow the installation prompts

## 🔧 Configuration

### Manifest V3 Compliance

This extension uses Manifest V3 for enhanced security and performance:

- **Service Worker**: Background script runs as a service worker
- **Declarative Net Request**: Network request modification
- **Content Scripts**: Page-level functionality injection
- **Secure Permissions**: Minimal required permissions

### Required Permissions

- `declarativeNetRequest`: Modify network requests for authentication
- `cookies`: Manage authentication cookies
- `tabs`: Access current tab information
- `scripting`: Inject content scripts
- `webNavigation`: Monitor page navigation
- `storage`: Store user preferences and session data

## 🏗️ Architecture

### Core Components

1. **Background Script** (`background.js`)
   - Service worker for core functionality
   - Authentication management
   - Cookie injection and management
   - API communication

2. **Content Script** (`content.js`)
   - Page-level interactions
   - Tool-specific enhancements
   - User interface injection
   - Real-time notifications

3. **Popup Interface** (`popup.html`, `popup.css`, `popup.js`)
   - User authentication
   - Tool management dashboard
   - Settings and preferences
   - Real-time status display

4. **Injected Script** (`injected.js`)
   - Deep page integration
   - Premium feature unlocking
   - Function overrides
   - Enhanced functionality

### Data Flow

```
User Action → Popup → Background Script → API Server
                ↓
Content Script → Injected Script → Target Service
                ↓
Service Response → User Interface
```

## 🔐 Security Features

### Authentication
- JWT-based token authentication
- Secure session management
- Automatic token refresh
- Encrypted credential storage

### Cookie Management
- Secure cookie injection
- Domain-specific cookie handling
- Automatic cookie refresh
- Encrypted cookie storage

### Privacy Protection
- No personal data collection
- Secure communication protocols
- Local data encryption
- Minimal permission requirements

## 🚀 Usage

### Getting Started

1. **Install the Extension**
   - Load the extension in Chrome
   - Click the Partagily icon in the toolbar

2. **Authenticate**
   - Enter your Partagily credentials
   - Wait for authentication confirmation

3. **Access Tools**
   - Navigate to any supported tool website
   - Click "Activate Partagily" when prompted
   - Enjoy premium features

### Tool Activation

1. **Automatic Detection**
   - Extension detects supported tool websites
   - Shows activation prompt automatically

2. **Manual Activation**
   - Click the extension icon
   - Select the tool to activate
   - Confirm activation

3. **Status Monitoring**
   - Real-time connection status
   - Tool activation indicators
   - Error notifications

## 🛠️ Development

### Project Structure

```
partagily-clean/
├── manifest.json          # Extension configuration
├── background.js          # Service worker
├── content.js            # Content script
├── popup.html            # Popup interface
├── popup.css             # Popup styles
├── popup.js              # Popup functionality
├── injected.js           # Page injection script
├── rules.json            # Network request rules
├── icons/                # Extension icons
└── README.md             # Documentation
```

### Development Setup

1. **Prerequisites**
   - Chrome browser
   - Text editor or IDE
   - Basic knowledge of JavaScript and Chrome APIs

2. **Local Development**
   ```bash
   # Clone the repository
   git clone <repository-url>
   cd partagily-clean
   
   # Load in Chrome
   # 1. Open chrome://extensions/
   # 2. Enable Developer mode
   # 3. Click "Load unpacked"
   # 4. Select the project folder
   ```

3. **Testing**
   - Test on supported tool websites
   - Verify authentication flow
   - Check cookie injection
   - Monitor console for errors

### Code Style

- **ES6+**: Modern JavaScript features
- **Async/Await**: Asynchronous operations
- **Error Handling**: Comprehensive error management
- **Documentation**: Inline comments and JSDoc
- **Security**: Secure coding practices

## 🔧 Configuration Options

### User Settings

- **Auto-Activation**: Automatically activate tools when detected
- **Notifications**: Show success/error notifications
- **Theme**: Light/dark interface theme
- **Privacy**: Data collection preferences

### Developer Settings

- **Debug Mode**: Enable console logging
- **API Endpoint**: Configure API server URL
- **Timeout Settings**: Request timeout configuration
- **Cache Settings**: Cookie and data caching options

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check credentials
   - Verify internet connection
   - Clear extension storage

2. **Tool Not Activating**
   - Refresh the page
   - Check if tool is supported
   - Verify authentication status

3. **Cookies Not Working**
   - Clear browser cookies
   - Disable other extensions
   - Check domain permissions

### Debug Information

- Check browser console for errors
- Verify extension permissions
- Monitor network requests
- Review extension storage

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

- **Documentation**: Check this README and inline comments
- **Issues**: Report bugs via GitHub issues
- **Contact**: <EMAIL>
- **Website**: https://partagily.com

## 🔄 Version History

### v1.0.0
- Initial release
- Basic authentication system
- Support for 6 major tools
- Chrome Extension V3 compliance
- Secure cookie management

---

**Note**: This extension is for educational and legitimate use only. Users are responsible for complying with the terms of service of the tools they access.
