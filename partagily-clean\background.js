/**
 * Partagily Background Service Worker
 * Handles core extension functionality, authentication, and tool management
 */

// Configuration and constants
const CONFIG = {
  API_BASE_URL: 'https://api.partagily.com',
  SUPPORTED_TOOLS: [
    'chatgpt.com',
    'notion.so',
    'figma.com',
    'canva.com',
    'grammarly.com',
    // Add more supported tools
  ],
  ENCRYPTION_KEY: 'partagily-secure-key-2024',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
};

// Global state management
let userSession = null;
let activeTools = new Map();
let cookieCache = new Map();

/**
 * Extension installation and startup
 */
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('Partagily extension installed:', details.reason);

  if (details.reason === 'install') {
    await initializeExtension();
  }
});

/**
 * Initialize extension on startup
 */
chrome.runtime.onStartup.addListener(async () => {
  console.log('Partagily extension starting up');
  await loadUserSession();
  await refreshToolAccess();
});

/**
 * Handle tab updates to inject tools when needed
 */
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const domain = extractDomain(tab.url);

    if (CONFIG.SUPPORTED_TOOLS.includes(domain)) {
      await handleToolAccess(tabId, domain);
    }
  }
});

/**
 * Handle messages from content scripts and popup
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'GET_USER_STATUS':
      sendResponse({
        authenticated: !!userSession,
        user: userSession?.user,
        activeTools: Array.from(activeTools.keys())
      });
      break;

    case 'AUTHENTICATE_USER':
      authenticateUser(message.credentials)
        .then(result => sendResponse(result))
        .catch(error => sendResponse({ error: error.message }));
      return true; // Keep message channel open for async response

    case 'ACTIVATE_TOOL':
      activateTool(message.toolDomain, sender.tab.id)
        .then(result => sendResponse(result))
        .catch(error => sendResponse({ error: error.message }));
      return true;

    case 'GET_TOOL_COOKIES':
      getToolCookies(message.domain)
        .then(cookies => sendResponse({ cookies }))
        .catch(error => sendResponse({ error: error.message }));
      return true;

    default:
      console.warn('Unknown message type:', message.type);
  }
});

/**
 * Initialize extension settings and data
 */
async function initializeExtension() {
  try {
    // Set up default settings
    await chrome.storage.local.set({
      settings: {
        autoActivate: true,
        notifications: true,
        theme: 'light'
      },
      lastUpdate: Date.now()
    });

    console.log('Extension initialized successfully');
  } catch (error) {
    console.error('Failed to initialize extension:', error);
  }
}

/**
 * Load user session from storage
 */
async function loadUserSession() {
  try {
    const result = await chrome.storage.local.get(['userSession']);

    if (result.userSession && !isSessionExpired(result.userSession)) {
      userSession = result.userSession;
      console.log('User session loaded:', userSession.user.email);
    } else {
      userSession = null;
      await chrome.storage.local.remove(['userSession']);
    }
  } catch (error) {
    console.error('Failed to load user session:', error);
  }
}

/**
 * Authenticate user with API
 */
async function authenticateUser(credentials) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials)
    });

    if (!response.ok) {
      throw new Error('Authentication failed');
    }

    const authData = await response.json();

    userSession = {
      token: authData.token,
      user: authData.user,
      expiresAt: Date.now() + CONFIG.SESSION_TIMEOUT
    };

    await chrome.storage.local.set({ userSession });
    await refreshToolAccess();

    return { success: true, user: authData.user };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Refresh access to all supported tools
 */
async function refreshToolAccess() {
  if (!userSession) return;

  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/tools/access`, {
      headers: {
        'Authorization': `Bearer ${userSession.token}`
      }
    });

    if (response.ok) {
      const toolsData = await response.json();

      for (const tool of toolsData.tools) {
        activeTools.set(tool.domain, {
          name: tool.name,
          cookies: tool.cookies,
          lastUpdated: Date.now()
        });
      }

      console.log('Tool access refreshed for', activeTools.size, 'tools');
    }
  } catch (error) {
    console.error('Failed to refresh tool access:', error);
  }
}

/**
 * Handle tool access for a specific domain
 */
async function handleToolAccess(tabId, domain) {
  if (!userSession || !activeTools.has(domain)) {
    return;
  }

  try {
    const toolData = activeTools.get(domain);

    // Inject cookies for the tool
    await injectToolCookies(domain, toolData.cookies);

    // Notify content script that tool is ready
    await chrome.tabs.sendMessage(tabId, {
      type: 'TOOL_ACTIVATED',
      domain: domain,
      toolName: toolData.name
    });

    console.log('Tool activated:', domain);
  } catch (error) {
    console.error('Failed to handle tool access:', error);
  }
}

/**
 * Inject cookies for a specific tool
 */
async function injectToolCookies(domain, cookies) {
  try {
    for (const cookie of cookies) {
      await chrome.cookies.set({
        url: `https://${domain}`,
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain || domain,
        path: cookie.path || '/',
        secure: true,
        httpOnly: cookie.httpOnly || false,
        expirationDate: cookie.expirationDate || (Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
      });
    }

    console.log('Cookies injected for:', domain);
  } catch (error) {
    console.error('Failed to inject cookies:', error);
  }
}

/**
 * Get cookies for a specific tool
 */
async function getToolCookies(domain) {
  try {
    const cookies = await chrome.cookies.getAll({
      domain: domain
    });

    return cookies.filter(cookie =>
      cookie.name.includes('auth') ||
      cookie.name.includes('session') ||
      cookie.name.includes('token')
    );
  } catch (error) {
    console.error('Failed to get cookies:', error);
    return [];
  }
}

/**
 * Utility functions
 */
function extractDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.replace('www.', '');
  } catch {
    return '';
  }
}

function isSessionExpired(session) {
  return !session.expiresAt || Date.now() > session.expiresAt;
}

/**
 * Activate a specific tool
 */
async function activateTool(domain, tabId) {
  if (!userSession) {
    throw new Error('User not authenticated');
  }

  if (!activeTools.has(domain)) {
    throw new Error('Tool not available in your subscription');
  }

  await handleToolAccess(tabId, domain);
  return { success: true };
}

// Error handling
chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension suspending, cleaning up...');
});

// Cleanup and error handling
window.addEventListener('beforeunload', () => {
  console.log('Background script unloading...');
});

// Handle extension errors
chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension suspending, saving state...');
  // Save any pending data
});

console.log('Partagily background script loaded');
