/**
 * Partagily Content Script
 * Handles page-level interactions, tool integration, and user interface
 */

// Content script configuration
const CONTENT_CONFIG = {
  NOTIFICATION_DURATION: 5000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  UI_INJECTION_DELAY: 2000
};

// Global state
let isToolActivated = false;
let currentDomain = '';
let notificationElement = null;
let retryCount = 0;

/**
 * Initialize content script when DOM is ready
 */
function initializeContentScript() {
  currentDomain = extractDomain(window.location.href);
  console.log('Partagily content script initialized for:', currentDomain);
  
  // Check if this is a supported tool
  checkToolSupport();
  
  // Set up message listeners
  setupMessageListeners();
  
  // Monitor for authentication changes
  monitorAuthenticationState();
  
  // Inject UI elements if needed
  setTimeout(injectUserInterface, CONTENT_CONFIG.UI_INJECTION_DELAY);
}

/**
 * Check if current domain is a supported tool
 */
async function checkToolSupport() {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_USER_STATUS'
    });
    
    if (response.authenticated && response.activeTools.includes(currentDomain)) {
      await activateToolFeatures();
    }
  } catch (error) {
    console.error('Failed to check tool support:', error);
  }
}

/**
 * Set up message listeners for communication with background script
 */
function setupMessageListeners() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.type) {
      case 'TOOL_ACTIVATED':
        handleToolActivation(message);
        break;
        
      case 'AUTHENTICATION_CHANGED':
        handleAuthenticationChange(message);
        break;
        
      case 'SHOW_NOTIFICATION':
        showNotification(message.text, message.type);
        break;
        
      case 'INJECT_COOKIES':
        handleCookieInjection(message.cookies);
        break;
        
      default:
        console.warn('Unknown message type in content script:', message.type);
    }
  });
}

/**
 * Handle tool activation
 */
function handleToolActivation(message) {
  if (message.domain === currentDomain) {
    isToolActivated = true;
    showNotification(`${message.toolName} activated successfully!`, 'success');
    
    // Apply tool-specific enhancements
    applyToolEnhancements(message.domain);
  }
}

/**
 * Apply tool-specific enhancements
 */
function applyToolEnhancements(domain) {
  switch (domain) {
    case 'chatgpt.com':
      enhanceChatGPT();
      break;
      
    case 'notion.so':
      enhanceNotion();
      break;
      
    case 'figma.com':
      enhanceFigma();
      break;
      
    case 'canva.com':
      enhanceCanva();
      break;
      
    case 'grammarly.com':
      enhanceGrammarly();
      break;
      
    default:
      console.log('No specific enhancements for:', domain);
  }
}

/**
 * ChatGPT specific enhancements
 */
function enhanceChatGPT() {
  console.log('Applying ChatGPT enhancements...');
  
  // Add premium features indicator
  addPremiumIndicator('ChatGPT Plus features enabled');
  
  // Monitor for premium features
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length > 0) {
        checkForPremiumFeatures();
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

/**
 * Notion specific enhancements
 */
function enhanceNotion() {
  console.log('Applying Notion enhancements...');
  addPremiumIndicator('Notion Pro features enabled');
  
  // Enable advanced features
  setTimeout(() => {
    const upgradeButtons = document.querySelectorAll('[data-testid="upgrade-button"]');
    upgradeButtons.forEach(button => {
      button.style.display = 'none';
    });
  }, 3000);
}

/**
 * Figma specific enhancements
 */
function enhanceFigma() {
  console.log('Applying Figma enhancements...');
  addPremiumIndicator('Figma Professional features enabled');
}

/**
 * Canva specific enhancements
 */
function enhanceCanva() {
  console.log('Applying Canva enhancements...');
  addPremiumIndicator('Canva Pro features enabled');
  
  // Hide upgrade prompts
  const style = document.createElement('style');
  style.textContent = `
    [data-testid="upgrade-prompt"],
    .upgrade-banner,
    .premium-badge {
      display: none !important;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Grammarly specific enhancements
 */
function enhanceGrammarly() {
  console.log('Applying Grammarly enhancements...');
  addPremiumIndicator('Grammarly Premium features enabled');
}

/**
 * Add premium features indicator
 */
function addPremiumIndicator(text) {
  const indicator = document.createElement('div');
  indicator.id = 'partagily-premium-indicator';
  indicator.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px 20px;
      border-radius: 25px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 600;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 10000;
      animation: slideIn 0.3s ease-out;
      cursor: pointer;
      transition: transform 0.2s ease;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <span style="margin-right: 8px;">✨</span>
      ${text}
      <span style="margin-left: 8px; opacity: 0.8;">via Partagily</span>
    </div>
    <style>
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    </style>
  `;
  
  document.body.appendChild(indicator);
  
  // Auto-hide after 10 seconds
  setTimeout(() => {
    if (indicator.parentNode) {
      indicator.style.animation = 'slideIn 0.3s ease-out reverse';
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.remove();
        }
      }, 300);
    }
  }, 10000);
}

/**
 * Check for premium features on the page
 */
function checkForPremiumFeatures() {
  const premiumSelectors = [
    '[data-testid="premium-feature"]',
    '.premium-only',
    '.upgrade-required',
    '[aria-label*="premium"]',
    '[aria-label*="upgrade"]'
  ];
  
  premiumSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      // Enable premium features
      element.classList.remove('disabled', 'locked');
      element.removeAttribute('disabled');
    });
  });
}

/**
 * Show notification to user
 */
function showNotification(text, type = 'info') {
  // Remove existing notification
  if (notificationElement) {
    notificationElement.remove();
  }
  
  const colors = {
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6'
  };
  
  notificationElement = document.createElement('div');
  notificationElement.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${colors[type] || colors.info};
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 10001;
      animation: slideDown 0.3s ease-out;
      max-width: 400px;
      text-align: center;
    ">
      ${text}
    </div>
    <style>
      @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
      }
    </style>
  `;
  
  document.body.appendChild(notificationElement);
  
  // Auto-remove notification
  setTimeout(() => {
    if (notificationElement && notificationElement.parentNode) {
      notificationElement.style.animation = 'slideDown 0.3s ease-out reverse';
      setTimeout(() => {
        if (notificationElement && notificationElement.parentNode) {
          notificationElement.remove();
        }
      }, 300);
    }
  }, CONTENT_CONFIG.NOTIFICATION_DURATION);
}

/**
 * Handle authentication state changes
 */
function handleAuthenticationChange(message) {
  if (message.authenticated) {
    showNotification('Successfully authenticated with Partagily', 'success');
    checkToolSupport();
  } else {
    showNotification('Authentication expired. Please log in again.', 'warning');
    isToolActivated = false;
  }
}

/**
 * Handle cookie injection
 */
function handleCookieInjection(cookies) {
  console.log('Handling cookie injection for', cookies.length, 'cookies');
  
  // Cookies are handled by the background script
  // This function can be used for any page-level cookie handling if needed
  
  showNotification('Authentication cookies updated', 'success');
}

/**
 * Monitor authentication state
 */
function monitorAuthenticationState() {
  // Check authentication periodically
  setInterval(async () => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_USER_STATUS'
      });
      
      if (!response.authenticated && isToolActivated) {
        isToolActivated = false;
        showNotification('Session expired. Please authenticate again.', 'warning');
      }
    } catch (error) {
      console.error('Failed to check authentication state:', error);
    }
  }, 60000); // Check every minute
}

/**
 * Inject user interface elements
 */
function injectUserInterface() {
  // Only inject UI on supported domains
  const supportedDomains = [
    'chatgpt.com',
    'notion.so',
    'figma.com',
    'canva.com',
    'grammarly.com'
  ];
  
  if (supportedDomains.includes(currentDomain)) {
    injectPartagilyButton();
  }
}

/**
 * Inject Partagily activation button
 */
function injectPartagilyButton() {
  const button = document.createElement('div');
  button.id = 'partagily-activation-button';
  button.innerHTML = `
    <button style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 25px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 10000;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <span>🚀</span>
      Activate Partagily
    </button>
  `;
  
  button.addEventListener('click', async () => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ACTIVATE_TOOL',
        toolDomain: currentDomain
      });
      
      if (response.success) {
        button.style.display = 'none';
      } else {
        showNotification(response.error || 'Failed to activate tool', 'error');
      }
    } catch (error) {
      showNotification('Please log in to Partagily first', 'warning');
    }
  });
  
  document.body.appendChild(button);
  
  // Hide button if tool is already activated
  if (isToolActivated) {
    button.style.display = 'none';
  }
}

/**
 * Utility functions
 */
function extractDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.replace('www.', '');
  } catch {
    return '';
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

console.log('Partagily content script loaded for:', window.location.hostname);
