/**
 * Partagily Popup Script
 * Handles popup interface interactions and user authentication
 */

// DOM elements
let elements = {};

// State management
let currentUser = null;
let isAuthenticated = false;
let currentTab = null;

// Supported tools configuration
const SUPPORTED_TOOLS = [
    {
        domain: 'chatgpt.com',
        name: 'ChatGPT Plus',
        icon: 'https://cdn.openai.com/API/favicon.ico',
        savings: 20
    },
    {
        domain: 'notion.so',
        name: 'Notion Pro',
        icon: 'https://www.notion.so/images/favicon.ico',
        savings: 10
    },
    {
        domain: 'figma.com',
        name: 'Figma Professional',
        icon: 'https://static.figma.com/app/icon/1/favicon.ico',
        savings: 15
    },
    {
        domain: 'canva.com',
        name: 'Canva Pro',
        icon: 'https://static.canva.com/web/images/favicon.ico',
        savings: 12
    },
    {
        domain: 'grammarly.com',
        name: 'Grammarly Premium',
        icon: 'https://static.grammarly.com/assets/files/favicon.ico',
        savings: 12
    },
    {
        domain: 'adobe.com',
        name: 'Adobe Creative Cloud',
        icon: 'https://www.adobe.com/favicon.ico',
        savings: 50
    }
];

/**
 * Initialize popup when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', async () => {
    initializeElements();
    setupEventListeners();
    await checkAuthenticationStatus();
    await getCurrentTab();
    updateInterface();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        // Sections
        authSection: document.getElementById('authSection'),
        dashboardSection: document.getElementById('dashboardSection'),
        
        // Authentication
        loginForm: document.getElementById('loginForm'),
        emailInput: document.getElementById('email'),
        passwordInput: document.getElementById('password'),
        loginBtn: document.getElementById('loginBtn'),
        
        // Status
        statusIndicator: document.getElementById('statusIndicator'),
        statusDot: document.statusIndicator?.querySelector('.status-dot'),
        statusText: document.statusIndicator?.querySelector('.status-text'),
        
        // User info
        userInitials: document.getElementById('userInitials'),
        userName: document.getElementById('userName'),
        userEmail: document.getElementById('userEmail'),
        subscriptionBadge: document.getElementById('subscriptionBadge'),
        
        // Stats
        activeToolsCount: document.getElementById('activeToolsCount'),
        totalSavings: document.getElementById('totalSavings'),
        
        // Tools
        toolsGrid: document.getElementById('toolsGrid'),
        currentTool: document.getElementById('currentTool'),
        currentToolIcon: document.getElementById('currentToolIcon'),
        currentToolName: document.getElementById('currentToolName'),
        currentToolStatus: document.getElementById('currentToolStatus'),
        activateCurrentTool: document.getElementById('activateCurrentTool'),
        
        // Buttons
        settingsBtn: document.getElementById('settingsBtn'),
        logoutBtn: document.getElementById('logoutBtn'),
        
        // Messages
        errorMessage: document.getElementById('errorMessage'),
        errorText: document.getElementById('errorText'),
        successMessage: document.getElementById('successMessage'),
        successText: document.getElementById('successText')
    };
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Login form
    if (elements.loginForm) {
        elements.loginForm.addEventListener('submit', handleLogin);
    }
    
    // Logout button
    if (elements.logoutBtn) {
        elements.logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Settings button
    if (elements.settingsBtn) {
        elements.settingsBtn.addEventListener('click', openSettings);
    }
    
    // Current tool activation
    if (elements.activateCurrentTool) {
        elements.activateCurrentTool.addEventListener('click', activateCurrentTool);
    }
    
    // Footer links
    document.getElementById('helpLink')?.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://partagily.com/help' });
    });
    
    document.getElementById('privacyLink')?.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://partagily.com/privacy' });
    });
    
    document.getElementById('termsLink')?.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://partagily.com/terms' });
    });
}

/**
 * Check authentication status
 */
async function checkAuthenticationStatus() {
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'GET_USER_STATUS'
        });
        
        isAuthenticated = response.authenticated;
        currentUser = response.user;
        
        updateStatusIndicator(isAuthenticated ? 'online' : 'offline');
        
    } catch (error) {
        console.error('Failed to check authentication status:', error);
        updateStatusIndicator('offline');
    }
}

/**
 * Get current active tab
 */
async function getCurrentTab() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        currentTab = tab;
    } catch (error) {
        console.error('Failed to get current tab:', error);
    }
}

/**
 * Update interface based on authentication status
 */
function updateInterface() {
    if (isAuthenticated && currentUser) {
        showDashboard();
        updateUserInfo();
        updateToolsGrid();
        updateCurrentTool();
        updateStats();
    } else {
        showAuthForm();
    }
}

/**
 * Show authentication form
 */
function showAuthForm() {
    if (elements.authSection) elements.authSection.style.display = 'block';
    if (elements.dashboardSection) elements.dashboardSection.style.display = 'none';
}

/**
 * Show dashboard
 */
function showDashboard() {
    if (elements.authSection) elements.authSection.style.display = 'none';
    if (elements.dashboardSection) elements.dashboardSection.style.display = 'block';
}

/**
 * Update status indicator
 */
function updateStatusIndicator(status) {
    if (!elements.statusDot || !elements.statusText) return;
    
    elements.statusDot.className = `status-dot ${status}`;
    
    const statusTexts = {
        online: 'Connected',
        offline: 'Offline',
        checking: 'Checking...'
    };
    
    elements.statusText.textContent = statusTexts[status] || 'Unknown';
}

/**
 * Update user information display
 */
function updateUserInfo() {
    if (!currentUser) return;
    
    if (elements.userInitials) {
        elements.userInitials.textContent = currentUser.name?.charAt(0).toUpperCase() || 'U';
    }
    
    if (elements.userName) {
        elements.userName.textContent = currentUser.name || 'User';
    }
    
    if (elements.userEmail) {
        elements.userEmail.textContent = currentUser.email || '';
    }
    
    if (elements.subscriptionBadge) {
        elements.subscriptionBadge.textContent = currentUser.subscription || 'Premium';
    }
}

/**
 * Update tools grid
 */
function updateToolsGrid() {
    if (!elements.toolsGrid) return;
    
    elements.toolsGrid.innerHTML = '';
    
    SUPPORTED_TOOLS.forEach(tool => {
        const toolElement = createToolCard(tool);
        elements.toolsGrid.appendChild(toolElement);
    });
}

/**
 * Create tool card element
 */
function createToolCard(tool) {
    const card = document.createElement('div');
    card.className = 'tool-card';
    card.innerHTML = `
        <div class="tool-icon">
            <img src="${tool.icon}" alt="${tool.name}" onerror="this.src='icons/icon48.png'">
        </div>
        <div class="tool-info">
            <h4>${tool.name}</h4>
            <p>Save $${tool.savings}/mo</p>
        </div>
    `;
    
    card.addEventListener('click', () => {
        chrome.tabs.create({ url: `https://${tool.domain}` });
    });
    
    return card;
}

/**
 * Update current tool section
 */
function updateCurrentTool() {
    if (!currentTab || !elements.currentTool) return;
    
    const currentDomain = extractDomain(currentTab.url);
    const tool = SUPPORTED_TOOLS.find(t => t.domain === currentDomain);
    
    if (tool) {
        elements.currentTool.style.display = 'block';
        
        if (elements.currentToolIcon) {
            elements.currentToolIcon.src = tool.icon;
            elements.currentToolIcon.alt = tool.name;
        }
        
        if (elements.currentToolName) {
            elements.currentToolName.textContent = tool.name;
        }
        
        if (elements.currentToolStatus) {
            elements.currentToolStatus.textContent = isAuthenticated ? 'Ready to activate' : 'Login required';
        }
        
        if (elements.activateCurrentTool) {
            elements.activateCurrentTool.disabled = !isAuthenticated;
        }
    } else {
        elements.currentTool.style.display = 'none';
    }
}

/**
 * Update statistics
 */
function updateStats() {
    if (elements.activeToolsCount) {
        elements.activeToolsCount.textContent = SUPPORTED_TOOLS.length;
    }
    
    if (elements.totalSavings) {
        const totalSavings = SUPPORTED_TOOLS.reduce((sum, tool) => sum + tool.savings, 0);
        elements.totalSavings.textContent = `$${totalSavings}`;
    }
}

/**
 * Handle login form submission
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const email = elements.emailInput?.value;
    const password = elements.passwordInput?.value;
    
    if (!email || !password) {
        showError('Please enter both email and password');
        return;
    }
    
    setLoginLoading(true);
    
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'AUTHENTICATE_USER',
            credentials: { email, password }
        });
        
        if (response.success) {
            currentUser = response.user;
            isAuthenticated = true;
            showSuccess('Successfully logged in!');
            updateInterface();
            updateStatusIndicator('online');
        } else {
            showError(response.error || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Connection error. Please try again.');
    } finally {
        setLoginLoading(false);
    }
}

/**
 * Handle logout
 */
async function handleLogout() {
    try {
        await chrome.storage.local.remove(['userSession']);
        currentUser = null;
        isAuthenticated = false;
        updateInterface();
        updateStatusIndicator('offline');
        showSuccess('Successfully logged out');
    } catch (error) {
        console.error('Logout error:', error);
        showError('Failed to logout');
    }
}

/**
 * Activate current tool
 */
async function activateCurrentTool() {
    if (!currentTab || !isAuthenticated) return;
    
    const currentDomain = extractDomain(currentTab.url);
    
    try {
        const response = await chrome.runtime.sendMessage({
            type: 'ACTIVATE_TOOL',
            toolDomain: currentDomain
        });
        
        if (response.success) {
            showSuccess('Tool activated successfully!');
            if (elements.currentToolStatus) {
                elements.currentToolStatus.textContent = 'Activated';
            }
        } else {
            showError(response.error || 'Failed to activate tool');
        }
    } catch (error) {
        console.error('Tool activation error:', error);
        showError('Failed to activate tool');
    }
}

/**
 * Open settings page
 */
function openSettings() {
    chrome.tabs.create({ url: 'https://partagily.com/settings' });
}

/**
 * Set login button loading state
 */
function setLoginLoading(loading) {
    if (!elements.loginBtn) return;
    
    const btnText = elements.loginBtn.querySelector('.btn-text');
    const btnLoader = elements.loginBtn.querySelector('.btn-loader');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoader.style.display = 'flex';
        elements.loginBtn.disabled = true;
    } else {
        btnText.style.display = 'block';
        btnLoader.style.display = 'none';
        elements.loginBtn.disabled = false;
    }
}

/**
 * Show error message
 */
function showError(message) {
    if (elements.errorText) elements.errorText.textContent = message;
    if (elements.errorMessage) elements.errorMessage.style.display = 'flex';
    
    setTimeout(() => {
        if (elements.errorMessage) elements.errorMessage.style.display = 'none';
    }, 5000);
}

/**
 * Show success message
 */
function showSuccess(message) {
    if (elements.successText) elements.successText.textContent = message;
    if (elements.successMessage) elements.successMessage.style.display = 'flex';
    
    setTimeout(() => {
        if (elements.successMessage) elements.successMessage.style.display = 'none';
    }, 3000);
}

/**
 * Extract domain from URL
 */
function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname.replace('www.', '');
    } catch {
        return '';
    }
}

console.log('Partagily popup script loaded');
