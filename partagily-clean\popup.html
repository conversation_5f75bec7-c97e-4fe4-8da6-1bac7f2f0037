<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partagily - Premium Access</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="Partagily" class="logo-icon">
                <h1 class="logo-text">Partagily</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Checking...</span>
            </div>
        </header>

        <!-- Authentication Section -->
        <section class="auth-section" id="authSection">
            <div class="auth-form">
                <h2>Sign In to Access Premium Tools</h2>
                <form id="loginForm">
                    <div class="input-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    </div>
                    <div class="input-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required placeholder="••••••••">
                    </div>
                    <button type="submit" class="btn btn-primary" id="loginBtn">
                        <span class="btn-text">Sign In</span>
                        <span class="btn-loader" style="display: none;">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </form>
                <div class="auth-links">
                    <a href="#" id="forgotPassword">Forgot password?</a>
                    <a href="#" id="createAccount">Create account</a>
                </div>
            </div>
        </section>

        <!-- Dashboard Section -->
        <section class="dashboard-section" id="dashboardSection" style="display: none;">
            <!-- User Info -->
            <div class="user-info">
                <div class="user-avatar">
                    <span id="userInitials">U</span>
                </div>
                <div class="user-details">
                    <h3 id="userName">User Name</h3>
                    <p id="userEmail"><EMAIL></p>
                    <span class="subscription-badge" id="subscriptionBadge">Premium</span>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="activeToolsCount">0</div>
                    <div class="stat-label">Active Tools</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalSavings">$0</div>
                    <div class="stat-label">Monthly Savings</div>
                </div>
            </div>

            <!-- Available Tools -->
            <div class="tools-section">
                <h3>Available Tools</h3>
                <div class="tools-grid" id="toolsGrid">
                    <!-- Tools will be populated by JavaScript -->
                </div>
            </div>

            <!-- Current Tab Tool -->
            <div class="current-tool" id="currentTool" style="display: none;">
                <h3>Current Page</h3>
                <div class="tool-card active">
                    <div class="tool-icon">
                        <img id="currentToolIcon" src="" alt="">
                    </div>
                    <div class="tool-info">
                        <h4 id="currentToolName">Tool Name</h4>
                        <p id="currentToolStatus">Ready to activate</p>
                    </div>
                    <button class="btn btn-small btn-primary" id="activateCurrentTool">
                        Activate
                    </button>
                </div>
            </div>

            <!-- Settings -->
            <div class="settings-section">
                <button class="btn btn-secondary" id="settingsBtn">
                    <span>⚙️</span>
                    Settings
                </button>
                <button class="btn btn-secondary" id="logoutBtn">
                    <span>🚪</span>
                    Logout
                </button>
            </div>
        </section>

        <!-- Error Messages -->
        <div class="error-message" id="errorMessage" style="display: none;">
            <span class="error-icon">⚠️</span>
            <span class="error-text" id="errorText">An error occurred</span>
        </div>

        <!-- Success Messages -->
        <div class="success-message" id="successMessage" style="display: none;">
            <span class="success-icon">✅</span>
            <span class="success-text" id="successText">Success!</span>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-links">
            <a href="#" id="helpLink">Help</a>
            <a href="#" id="privacyLink">Privacy</a>
            <a href="#" id="termsLink">Terms</a>
        </div>
        <div class="footer-version">
            v1.0.0
        </div>
    </footer>

    <script src="popup.js"></script>
</body>
</html>
