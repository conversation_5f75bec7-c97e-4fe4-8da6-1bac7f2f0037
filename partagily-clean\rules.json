[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}]}, "condition": {"urlFilter": "*://*.chatgpt.com/*", "resourceTypes": ["main_frame", "sub_frame", "xmlhttprequest"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "set", "value": "https://chat.openai.com/"}]}, "condition": {"urlFilter": "*://chat.openai.com/backend-api/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 3, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "set", "value": "https://www.notion.so"}]}, "condition": {"urlFilter": "*://*.notion.so/api/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 4, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "X-Requested-With", "operation": "set", "value": "XMLHttpRequest"}]}, "condition": {"urlFilter": "*://*.figma.com/api/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 5, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Accept", "operation": "set", "value": "application/json, text/plain, */*"}]}, "condition": {"urlFilter": "*://*.canva.com/api/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 6, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Accept-Language", "operation": "set", "value": "en-US,en;q=0.9"}]}, "condition": {"urlFilter": "*://*.grammarly.com/*", "resourceTypes": ["main_frame", "sub_frame", "xmlhttprequest"]}}, {"id": 7, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "X-Frame-Options", "operation": "remove"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 8, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Content-Security-Policy", "operation": "remove"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame"]}}]